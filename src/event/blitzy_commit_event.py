from typing import Any, Dict, Optional

import requests
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (BlitzyCommit, BlitzyCommitPRAction,
                                  BlitzyCommitStatus, GithubInstallation,
                                  GitHubProjectRepo, Status,
                                  VersionControlSystem)
from sqlalchemy.orm import Session

from src.service.github_handler_service import get_access_token_by_repo_id


def process_blitzy_commit_event(payload: Dict[str, Any], session: Optional[Session] = None):
    """
    Processes the reverse code generation completion event.

    :param payload: Dictionary containing event data.
    :param session: Optional database session for executing queries. If not provided,
        a new session will be created within the function.
    """
    # Get git_project_repo_id from the top level of payload, not metadata
    git_project_repo_id = payload.get('git_project_repo_id', "")

    if not git_project_repo_id:
        logger.error("No git_project_repo_id found in payload")
        return

    git_installation, git_project_repo = get_github_installation_by_github_project_repo_id(git_project_repo_id)

    if not git_installation:
        logger.error(f"No git installation found for git project repo id: {git_project_repo_id}")
        return

    # Check for Azure DevOps indicator in metadata
    if git_installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        process_code_generation_event_azure(payload, git_project_repo.repo_id, session)
    elif git_installation.svc_type == VersionControlSystem.GITHUB:
        process_code_generation_event_github(payload, session)
    else:
        logger.error(f"Invalid svc_type found when processing a commit event {git_installation.svc_type}")


def process_code_generation_event_github(payload: Dict[str, Any], session: Optional[Session] = None):
    """Process code generation event for GitHub platform."""
    project_phase = payload.get("phase")
    job_status = payload.get("status")
    code_gen_id = payload["code_gen_id"]
    project_run_id = payload["jobId"]
    metadata = payload.get("metadata", {})
    pr_data = metadata.get("pr_data", {})

    with get_db_session(session) as session:
        logger.info(f"In code generation received {project_phase} event for GitHub.")
        if not job_status == Status.DONE.value:
            logger.info("Job status is not DONE. Skipping.")
            return

        if not pr_data:
            logger.warning(f"Neither PR data nor commit data found in {payload}. Event won't be processed.")
            return

        logger.info("Processing GitHub PR data from event")
        blitzy_commit = create_blitzy_commit_from_pr_github(pr_data, metadata, project_run_id, code_gen_id)
        session.add(blitzy_commit)
        session.flush()
        logger.info(f"Successfully created blitzy commit from GitHub PR for {project_run_id} and {code_gen_id}.")


def process_code_generation_event_azure(payload: Dict[str, Any], repo_id: str, session: Optional[Session] = None):
    """Process code generation event for Azure DevOps platform."""
    project_phase = payload.get("phase")
    job_status = payload.get("status")
    code_gen_id = payload["code_gen_id"]
    project_run_id = payload["jobId"]
    metadata = payload.get("metadata", {})
    pr_data = metadata.get("pr_data", {})

    with get_db_session(session) as session:
        logger.info(f"In code generation received {project_phase} event for Azure DevOps.")
        if not job_status == Status.DONE.value:
            logger.info("Job status is not DONE. Skipping.")
            return

        if not pr_data:
            logger.warning(f"Neither PR data nor commit data found in {payload}. Event won't be processed.")
            return

        logger.info("Processing Azure DevOps PR data from event")
        blitzy_commit = create_blitzy_commit_from_pr_azure(pr_data, metadata, project_run_id, code_gen_id, repo_id)
        session.add(blitzy_commit)
        session.flush()
        logger.info(f"Successfully created blitzy commit from Azure DevOps PR for {project_run_id} and {code_gen_id}.")


def process_forward_prop_event(payload: Dict[str, Any], session: Optional[Session] = None):
    project_phase = payload.get("phase")
    job_status = payload.get("status")
    code_gen_id = payload["code_gen_id"]
    project_run_id = payload["jobId"]
    with get_db_session(session) as session:
        logger.info(f"In code generation received {project_phase} event.")
        if not job_status == Status.DONE.value:
            logger.info("Job status is not DONE. Skipping.")
            return

        repo_url = payload.get("metadata").get("repo_url")
        blitzy_commit = create_blitzy_commit_from_repo_url(repo_url, project_run_id, code_gen_id)
        session.add(blitzy_commit)
        session.flush()
        logger.info(f"Successfully created blitzy commit for {project_run_id} and {code_gen_id}.")
        return


def extract_pr_data(pr_data) -> Dict[str, Any]:
    """
    Summarizes and extracts pull request (PR) related data, repository details, branch
    information, commit SHA values, and user information into a structured dictionary.

    :param pr_data: The dictionary containing pull request details from an API response.
    :return: A dictionary containing extracted PR data, repository information,
        branch and commit details, and user information.
    """
    # Get repository information
    repo_name = pr_data.get("base", {}).get("repo", {}).get("name")
    org_name = pr_data.get("base", {}).get("repo", {}).get("owner", {}).get("login")

    # Construct repo_url
    repo_url = f"https://github.com/{org_name}/{repo_name}" if org_name and repo_name else None

    return {
        # PR specific fields
        "pr_number": pr_data.get("number"),
        "pr_link": pr_data.get("html_url"),
        "pr_state": pr_data.get("state"),
        "pr_title": pr_data.get("title"),
        "pr_body": pr_data.get("body"),
        "pr_created_at": pr_data.get("created_at"),
        "pr_updated_at": pr_data.get("updated_at"),

        # Repository information
        "repo_id": str(pr_data.get("base", {}).get("repo", {}).get("id")),
        "repo_name": repo_name,
        "org_name": org_name,
        "repo_url": repo_url,

        # Branch information
        "head_branch": pr_data.get("head", {}).get("ref"),
        "base_branch": pr_data.get("base", {}).get("ref"),

        # Commit information
        "head_commit_sha": pr_data.get("head", {}).get("sha"),
        "base_commit_sha": pr_data.get("base", {}).get("sha"),

        # User information
        "user_login": pr_data.get("user", {}).get("login"),
        "user_id": str(pr_data.get("user", {}).get("id")),
    }


def extract_pr_data_azure(pr_data: Dict[str, Any], metadata: Dict[str, Any], repo_id: str) -> Dict[str, Any]:
    """Extract PR data specifically for Azure DevOps format."""
    # Get repo info from metadata since Azure PR data structure is different
    repo_name = metadata.get("repo_name")
    repo_url = metadata.get("repo_url")

    # In case of azure we have to call azure API to get info about PR. Because, unlike github azure sdk `create_pr`
    # call doesn't give this info. Consider in the future moving this logic to archie-github-handler
    access_token = get_access_token_by_repo_id(repo_id)

    if not access_token:
        raise Exception(f"No access token found for repo id {repo_id}, can't get azure PR data.")

    # Make API call to Azure DevOps to get detailed PR information
    pr_api_url = pr_data.get('url')
    try:
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        logger.info(
            f"Making API call to Azure DevOps to get PR details for PR {pr_data.get('id')} using URL {pr_api_url}"
        )
        response = requests.get(pr_api_url, headers=headers)
        response.raise_for_status()
        pr_details = response.json()
        logger.info(f"Successfully fetched PR details from Azure DevOps API for PR {pr_data.get('id')}")
    except Exception as e:
        logger.error(f"Failed to fetch PR details from Azure DevOps API: {e}")
        raise

    # Extract web URL for PR from the API response
    web_url = pr_details['repository']['webUrl']
    pr_id = pr_details.get('pullRequestId')
    pr_web_link = f"{web_url}/pullrequest/{pr_id}"

    # Try to extract org from Azure DevOps URL
    if web_url and 'dev.azure.com' in web_url:
        url_parts = web_url.split("/")
        if len(url_parts) > 3:
            org_name = url_parts[3]  # e.g., "blitzy-qa"
        else:
            raise Exception(f"Unable to extract org name from Azure DevOps URL: {web_url}")
    else:
        raise Exception(f"Unable to extract org name from Azure DevOps URL: {web_url}")

    # Extract commit SHAs from the detailed PR response
    head_commit_sha = None
    base_commit_sha = None
    repo_web_url = None

    # Get head commit SHA from lastMergeSourceCommit
    if pr_details.get('lastMergeSourceCommit', {}).get('commitId'):
        head_commit_sha = pr_details['lastMergeSourceCommit']['commitId']

    # Get base commit SHA from lastMergeTargetCommit
    if pr_details.get('lastMergeTargetCommit', {}).get('commitId'):
        base_commit_sha = pr_details['lastMergeTargetCommit']['commitId']

    # Get repository web URL
    if pr_details.get('repository', {}).get('webUrl'):
        repo_web_url = pr_details['repository']['webUrl']

    enhanced_pr_data = {
        # Azure DevOps actual field names from your payload
        "pr_number": pr_data.get("id"),  # Azure uses 'id' not 'number'
        "pr_link": pr_web_link,
        "pr_state": pr_data.get("status"),  # Azure uses 'status' not 'state'
        "pr_title": pr_data.get("title"),
        "pr_body": pr_data.get("description"),  # Azure uses 'description' not 'body'
        "pr_created_at": pr_data.get("created_date"),  # Azure uses 'created_date' not 'created_at'
        "pr_updated_at": pr_details['repository']['project']['lastUpdateTime'],

        # Repository info from metadata and API response
        "repo_id": repo_id,  # Azure uses GUIDs, not easily accessible
        "repo_name": repo_name,
        "org_name": org_name,
        "repo_url": repo_web_url or repo_url,  # Use web URL if available, fallback to metadata

        # Branch info - Azure uses direct fields
        "head_branch": pr_data.get("source_ref", "").replace("refs/heads/", ""),  # Remove refs prefix
        "base_branch": pr_data.get("target_ref", "").replace("refs/heads/", ""),  # Remove refs prefix

        # Commit info - now available from API response
        "head_commit_sha": head_commit_sha,
        "base_commit_sha": base_commit_sha,

        "user_login": pr_details.get("createdBy", {}).get("uniqueName"),
        "user_id": pr_details.get("createdBy", {}).get("id")
    }
    logger.info(
        f"Successfully extracted PR data from Azure DevOps API for PR "
        f"{pr_data.get('id')}. Data {enhanced_pr_data}"
    )
    return enhanced_pr_data


def create_blitzy_commit_from_pr_github(
        pr_data: Dict[str, Any], metadata: Dict[str, Any], project_run_id: str, code_gen_id: str
) -> BlitzyCommit:
    """Creates a BlitzyCommit instance from GitHub pull request data."""
    extracted_data = extract_pr_data(pr_data)

    branch_url = None
    if extracted_data["repo_url"] and extracted_data["head_branch"]:
        branch_url = f"{extracted_data['repo_url']}/tree/{extracted_data['head_branch']}"

    return BlitzyCommit(
        project_run_id=project_run_id,
        code_gen_id=code_gen_id,
        org_name=extracted_data["org_name"],
        repo_name=extracted_data["repo_name"],
        repo_id=extracted_data["repo_id"],
        repo_url=extracted_data["repo_url"],
        branch_name=extracted_data["head_branch"],
        version_control_system=VersionControlSystem.GITHUB,
        blitzy_commit_hash=extracted_data["head_commit_sha"],
        blitzy_commit_url=(
            f"https://github.com/{extracted_data['org_name']}/{extracted_data['repo_name']}/"
            f"commit/{extracted_data['head_commit_sha']}"
            if extracted_data["org_name"] and extracted_data["repo_name"] and extracted_data["head_commit_sha"]
            else None
        ),
        blitzy_branch_url=branch_url,
        status=BlitzyCommitStatus.PENDING,
        pr_number=int(extracted_data["pr_number"]),
        pr_link=extracted_data["pr_link"],
        pr_action=BlitzyCommitPRAction.NOT_TAKEN,
        commit_metadata=pr_data,
    )


def create_blitzy_commit_from_pr_azure(
        pr_data: Dict[str, Any], metadata: Dict[str, Any],
        project_run_id: str, code_gen_id: str, repo_id: str
) -> BlitzyCommit:
    """Creates a BlitzyCommit instance from Azure DevOps pull request data."""
    extracted_data = extract_pr_data_azure(pr_data, metadata, repo_id)

    branch_url = None
    if extracted_data["repo_url"] and extracted_data["head_branch"]:
        # Azure DevOps branch URL format
        branch_url = f"{extracted_data['repo_url']}?version=GB{extracted_data['head_branch']}"

    return BlitzyCommit(
        project_run_id=project_run_id,
        code_gen_id=code_gen_id,
        org_name=extracted_data["org_name"],
        repo_name=extracted_data["repo_name"],
        repo_id=extracted_data["repo_id"],
        repo_url=extracted_data["repo_url"],
        branch_name=extracted_data["head_branch"],
        version_control_system=VersionControlSystem.AZURE_DEVOPS,
        blitzy_commit_hash=extracted_data["head_commit_sha"],
        blitzy_commit_url=(
            f"{extracted_data['repo_url']}/commit/{extracted_data['head_commit_sha']}"
            if extracted_data["repo_url"] and extracted_data["head_commit_sha"]
            else None
        ),
        blitzy_branch_url=branch_url,
        status=BlitzyCommitStatus.PENDING,
        pr_number=int(extracted_data["pr_number"]) if extracted_data["pr_number"] else None,
        pr_link=extracted_data["pr_link"],
        pr_action=BlitzyCommitPRAction.NOT_TAKEN,
        commit_metadata=pr_data,
    )


def create_blitzy_commit_from_repo_url(repo_url: str, project_run_id: str, code_gen_id: str):
    """
    Creates a BlitzyCommit instance from a given GitHub repository URL.

    :param repo_url: The URL of the GitHub repository.
    :param project_run_id: Unique identifier for the project run.
    :param code_gen_id: Unique identifier for the code generation.
    :return: BlitzyCommit instance with populated fields.
    """
    try:
        # Remove protocol and domain
        path = repo_url.replace('https://github.com/', '')

        # Split the path into parts
        parts = path.strip('/').split('/')

        if len(parts) >= 2:
            org_name = parts[0]
            repo_name = parts[1]
        else:
            org_name = None
            repo_name = None

    except Exception as e:
        logger.error(f"Error parsing repo URL: {e}")
        org_name = None
        repo_name = None

    return BlitzyCommit(
        project_run_id=project_run_id,
        code_gen_id=code_gen_id,
        org_name=org_name,
        repo_name=repo_name,
        repo_url=repo_url,
        version_control_system=VersionControlSystem.GITHUB,
        status=BlitzyCommitStatus.PENDING,
        pr_action=None,
        commit_metadata={"repo_url": repo_url}
    )


def create_blitzy_commit_from_commit(commit_data: Dict[str, Any], payload: Dict[str, Any], project_run_id: str,
                                     code_gen_id: str) -> BlitzyCommit:
    """
    Creates a BlitzyCommit instance from commit data.

    :param commit_data: Commit metadata consumed to generate the BlitzyCommit.
    :param payload: The original event payload containing additional context.
    :param project_run_id: Unique identifier for the project run.
    :param code_gen_id: Identifier for the code generation associated with the commit.
    :return: A BlitzyCommit instance constructed from the provided commit data.
    """
    # Extract organization and repository information
    org_name = payload.get("org_name", "")
    repo_name = payload.get("metadata", {}).get("repo_name", "")
    repo_id = payload.get("repo_id", "")
    branch_name = payload.get("branch_name", "")

    # Extract repo URL from commit html_url
    commit_html_url = commit_data.get("html_url", "")
    repo_url = None

    if commit_html_url:
        # Format: https://github.com/BhagyashreeBlitzy/hao-backprop-test/commit/eceb6a220cd78fbb3e3f49028c53001a19a00f95
        # We need to extract: https://github.com/BhagyashreeBlitzy/hao-backprop-test
        parts = commit_html_url.split('/commit/')
        if len(parts) > 0:
            repo_url = parts[0]  # This gives us the repo URL without the commit part

    # If we couldn't extract from commit URL, try constructing from org and repo name
    if not repo_url and org_name and repo_name:
        repo_url = f"https://github.com/{org_name}/{repo_name}"

    # Create branch URL
    branch_url = None
    if repo_url and branch_name:
        branch_url = f"{repo_url}/tree/{branch_name}"

    # Extract commit specific information
    commit_hash = commit_data.get("sha")
    commit_url = commit_data.get("html_url")

    # Get original head commit from parents
    original_head_commit_hash = None
    parents = commit_data.get("parents", [])
    if parents and len(parents) > 0:
        original_head_commit_hash = parents[0].get("sha")

    return BlitzyCommit(
        project_run_id=project_run_id,
        code_gen_id=code_gen_id,
        org_name=org_name,
        repo_name=repo_name,
        repo_id=repo_id,
        repo_url=repo_url,
        branch_name=branch_name,
        blitzy_branch_url=branch_url,
        version_control_system=VersionControlSystem.GITHUB,
        blitzy_commit_hash=commit_hash,
        blitzy_commit_url=commit_url,
        status=BlitzyCommitStatus.PENDING,
        original_head_commit_hash=original_head_commit_hash,
        pr_action=BlitzyCommitPRAction.NOT_TAKEN,
        commit_metadata=commit_data,
    )


def get_github_installation_by_github_project_repo_id(
        github_project_repo_id: str,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get GitHub installation for a specific repository.

    :param repo_id: ID of the repository to find installation for.
    :param session: Database session to use, can be None.
    :return: GithubInstallation object if found, otherwise None.
    """
    with get_db_session(session) as db_session:
        # Find the project repo record
        # Choose first record, maps to same org_id
        github_project_repo = db_session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.id == github_project_repo_id
        ).first()

        if not github_project_repo:
            logger.debug(f"Unable to fetch github project repo from db with project_repo_id {github_project_repo_id}")
            return None

        # Find the GitHub installation for this organization
        # Assume there is always one record, order by created_at for determinism
        github_installation = db_session.query(GithubInstallation).filter(
            GithubInstallation.target_id == github_project_repo.org_id
        ).order_by(GithubInstallation.created_at.desc()).first()

        if not github_installation:
            logger.debug(
                f"Unable to fetch github installation from db with project_repo_id {github_project_repo_id} \
                and org_id {github_project_repo.org_id}"
            )
            return None

        return (github_installation, github_project_repo)
