import os

from blitzy_utils.service_client import create_service_error_handler
from flask_utils.base_error import BaseError
from google.cloud import storage

GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
storage_client = storage.Client()
PLATFORM_URL = os.environ["PLATFORM_URL"]
SENDGRID_VALIDATION_API_KEY = os.environ["SENDGRID_VALIDATION_API_KEY"]

check_service_error = create_service_error_handler(BaseError)
