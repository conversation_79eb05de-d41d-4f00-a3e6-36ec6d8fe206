from typing import Any, Dict, List

from common_models.models import User
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import map_to_model

from src.api.models import (Branch, BranchList, DefaultBranchOutput,
                            GetGithubInstallationOutput,
                            GetGithubInstallationOutputArray, GithubOrgBasic,
                            GithubOrgBasicList, PRActionInput, Repository,
                            RepositoryList)
from src.api.utils.github_utils import (get_branches_by_repo_id,
                                        get_orgs_by_installations,
                                        get_repo_by_id,
                                        get_repos_by_installation)
from src.error.errors import ResourceNotFound
from src.github.github_app_service import GithubAppService
from src.service.github_installation_access_service import (
    get_github_installation_by_user_and_target_name,
    get_github_installations_by_user)
from src.service.user_service import get_user_by_id

users_bp = Blueprint("users", __name__, url_prefix="/users")


@users_bp.route("/<user_id>/installations", methods=["GET"])
@flask_pydantic_response
def get_installation_by_user_id(user_id: str):
    """Get the github installation by user id."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")
    installation_list = get_github_installations_by_user(user_id)
    result = map_installation_list_to_model(installation_list)
    return result, 200


@users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_organizations_by_user_id(user_id: str):
    """Get the github organizations by user id."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")
    org_list = get_organization_by_user(user_info)
    response = map_org_list_to_pydantic(org_list)
    return response, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories", methods=["GET"])
@flask_pydantic_response
def get_repositories_by_user_id(user_id: str, org_name: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    repo_list = get_repos_by_user(user_info, org_name)
    response = map_repo_list_to_pydantic(repo_list)
    return response, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/branches", methods=["GET"])
@flask_pydantic_response
def get_branches_by_repo_id_and_installation_id(user_id: str, org_name: str, repo_id: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")
    branch_list = get_branches_by_repo_id(installation.installation_id, repo_id)
    output = map_branches_to_pydantic(branch_list)
    return output, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>", methods=["GET"])
@flask_pydantic_response
def get_repo_by_repo_id(user_id: str, org_name: str, repo_id: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    repo_response = get_repo_by_id(installation.installation_id, repo_id)
    response = Repository(**repo_response)
    return response, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/default/branch", methods=["GET"])
@flask_pydantic_response
def get_default_branches_by_repo_id(user_id: str, org_name: str, repo_id: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    github_service = GithubAppService()
    branch_output = github_service.get_default_branch(repo_id, installation.installation_id)
    response = DefaultBranchOutput(**branch_output)
    return response, 200


# TODO: consider rethinking api, branch name still can cause issues if has postfil /head
@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/branch/<path:branch_name>/head/commit",
                methods=["GET"])
@flask_pydantic_response
def get_branch_head_commit(user_id: str, org_name: str, repo_id: str, branch_name: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    github_service = GithubAppService()
    branch_commit_output = github_service.get_branch_head_commit(repo_id, installation.installation_id, branch_name)
    if not branch_commit_output:
        raise ResourceNotFound(f"Commit information not found for {branch_name}")
    return branch_commit_output, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/pr/<pr_number>/action",
                methods=["POST"])
@validate_request(PRActionInput)
@flask_pydantic_response
def post_pr_action_by_repo_id(user_id: str, org_name: str, repo_id: str, pr_number: str, payload: PRActionInput):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    github_service = GithubAppService()
    pr_action_status = github_service.manage_pull_request(user_info.id, installation.installation_id, repo_id,
                                                          int(pr_number), payload.action.value)
    return pr_action_status, 200


def map_installation_list_to_model(installation_list) -> GetGithubInstallationOutputArray:
    mapped_list = [map_to_model(installation, GetGithubInstallationOutput) for installation in installation_list]
    output_list = GetGithubInstallationOutputArray(results=mapped_list)
    return output_list


def get_organization_by_user(user_info: User):
    installation_list = get_github_installations_by_user(user_info.id)
    org_list = get_orgs_by_installations(installation_list)
    return org_list


def map_org_list_to_pydantic(org_list: List[Dict[str, Any]]):
    mapped_list = []
    for org in org_list:
        info = next(iter(org.values()))
        mapped_list.append(GithubOrgBasic(**info))

    github_org_list = GithubOrgBasicList(results=mapped_list)
    return github_org_list


def map_repo_list_to_pydantic(repo_list: List[Dict[str, Any]]):
    mapped_list = []
    for repo in repo_list:
        if repo is None:
            continue
        info = next(iter(repo.values()))
        for _, value in info.items():
            mapped_list.append(Repository(**value))

    github_repo_list = RepositoryList(results=mapped_list)
    return github_repo_list


def get_repos_by_user(user_info: User, org_name: str):
    installation_list = get_github_installations_by_user(user_info.id)
    repo_list = get_repos_by_installation(installation_list, org_name)
    return repo_list


def map_branches_to_pydantic(branches: Dict[str, Any]) -> BranchList:
    mapped_list = []
    for branch in branches["branches"]:
        branch = Branch(**branch)
        mapped_list.append(branch)

    branch_list = BranchList(results=mapped_list)
    return branch_list
