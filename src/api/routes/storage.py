from blitzy_utils.common import gcs_bucket_walk
from blitzy_utils.logger import logger
from flask import Blueprint, Response, request, stream_with_context
from flask_utils.decorators import flask_pydantic_response, validate_request

from src.api.models import (BucketWalkOutput, FileUploadOutput,
                            StringUploadInput, FileCopyInput, Status200)
from src.consts import GCS_BUCKET_NAME, storage_client
from src.error.errors import InvalidRequest
from src.utils.gcs_utils import get_company_bucket_name, copy_file_from_default_bucket, \
    copy_folder_from_default_bucket, gcs_upload_string, create_bucket_if_not_exists, copy_file, UPLOAD_TIMEOUT
from src.utils.url_utils import URLUtils

storage_bp = Blueprint("storage", __name__, url_prefix="/v1/storage")


@storage_bp.route("/bucket-walk", methods=["GET"])
@flask_pydantic_response
def bucket_walk():
    """
    Walk through a GCS bucket and return directory structure
    """
    company_id = request.args.get("company_id")
    prefix = request.args.get("prefix", "")

    logger.info(f"Received request to walk bucket for company {company_id} with prefix '{prefix}'")
    company_bucket_name = get_company_bucket_name(company_id=company_id)

    create_bucket_if_not_exists(company_bucket_name)

    company_bucket = storage_client.bucket(company_bucket_name)
    prefix_exists = False

    if prefix:
        blobs = list(company_bucket.list_blobs(prefix=prefix, max_results=1))
        prefix_exists = len(blobs) > 0
    else:
        blobs = list(company_bucket.list_blobs(max_results=1))
        prefix_exists = len(blobs) > 0

    # If prefix doesn't exist in company bucket, try to copy from default bucket
    if not prefix_exists:
        logger.info(f"Prefix '{prefix}' not found in company bucket {company_bucket_name}. Checking default bucket.")

        # Check if the prefix exists in default bucket
        default_bucket = storage_client.bucket(GCS_BUCKET_NAME)
        default_blobs = list(default_bucket.list_blobs(prefix=prefix, max_results=1))

        if default_blobs:
            logger.info(f"Found prefix '{prefix}' in default bucket. Copying folder structure to company bucket.")
            copy_folder_from_default_bucket(prefix, GCS_BUCKET_NAME, company_bucket_name)
        else:
            message = (f"Prefix '{prefix}' not found in either company bucket"
                       f" {company_bucket_name} or default bucket {GCS_BUCKET_NAME}")
            logger.warning(message)
            raise InvalidRequest(message)

    # Now walk through the bucket (after potential copying)
    walk_results = []
    for path, subfolders, files in gcs_bucket_walk(storage_client, company_bucket_name, prefix):
        walk_results.append({
            "path": path,
            "subfolders": subfolders,
            "files": files
        })

    logger.info(f"Successfully walked bucket {company_bucket_name}, found {len(walk_results)} directories")

    return BucketWalkOutput(
        bucket_name=company_bucket_name,
        prefix=prefix,
        results=walk_results
    ), 200


@storage_bp.route("/upload", methods=["POST"])
@flask_pydantic_response
def upload_file():
    file_path = request.args.get("file_path")
    company_id = request.args.get("company_id")

    logger.info(
        f"Received request to upload file to {GCS_BUCKET_NAME}/{file_path} for company {company_id}"
    )

    if not file_path:
        raise InvalidRequest("file_path query parameter is required")

    file_path = URLUtils.decode_file_path(file_path)
    content_type = request.headers.get("Content-Type", "application/octet-stream")

    file_path = URLUtils.validate_file_path(file_path)
    logger.info(f"File path validated successfully: {file_path}")

    company_bucket_name = get_company_bucket_name(company_id=company_id)
    create_bucket_if_not_exists(company_bucket_name)
    company_file_path = f"{company_bucket_name}/{file_path}"
    logger.info(f"Uploading file to {company_file_path}")
    bucket = storage_client.bucket(company_bucket_name)
    blob = bucket.blob(file_path)
    blob.upload_from_file(
        request.stream,
        content_type=content_type,
        timeout=UPLOAD_TIMEOUT
    )
    logger.info("File uploaded successfully")
    return FileUploadOutput(filePath=company_file_path), 201


@storage_bp.route("/upload-string", methods=["POST"])
@validate_request(StringUploadInput)
@flask_pydantic_response
def upload_string(payload: StringUploadInput):
    company_id = payload.companyId
    file_path = payload.filePath
    data = payload.data
    content_type = payload.contentType if payload.contentType else "text/plain"

    logger.info(f"Received request to upload string data to {file_path} for company {company_id}")

    company_bucket_name = get_company_bucket_name(company_id=company_id)
    create_bucket_if_not_exists(company_bucket_name)

    gcs_upload_string(company_bucket_name, file_path, data, content_type=content_type)

    gcs_path = f"gs://{company_bucket_name}/{file_path}"
    logger.info(f"Uploaded to: {gcs_path}")

    return FileUploadOutput(filePath=gcs_path), 201


@storage_bp.route("/download", methods=["GET"])
def download_file():
    encoded_file_path = request.args.get("file_path")
    company_id = request.args.get("company_id")
    if not encoded_file_path:
        raise InvalidRequest("file_path query parameter is required")

    file_path = URLUtils.decode_file_path(encoded_file_path)
    file_path = URLUtils.validate_file_path(file_path)
    as_text = request.args.get("as_text", "false").lower() == "true"

    company_bucket_name = get_company_bucket_name(company_id=company_id)

    bucket = storage_client.bucket(company_bucket_name)
    blob = bucket.blob(file_path)
    if not blob.exists():
        logger.warning(f"File {file_path} not found in bucket {company_bucket_name}")
        blob = copy_file_from_default_bucket(file_path, GCS_BUCKET_NAME, company_bucket_name)

    logger.info(f"Downloading file: gs://{company_bucket_name}/{file_path}")
    if as_text:
        try:
            text_content = blob.download_as_text()
            logger.info(f"Downloaded as text: {len(text_content)} characters")
            return text_content
        except UnicodeDecodeError as e:
            logger.error(f"Cannot decode binary file as text: {str(e)}")
            raise InvalidRequest("Cannot download binary file as text. "
                                 "Remove as_text=true parameter for binary files like PDF, images, videos, etc.")

    def generate_file_stream():
        """Generate file content in chunks"""
        with blob.open("rb") as f:
            while True:
                chunk = f.read(1024 * 1024)  # 1MB chunks
                if not chunk:
                    break
                yield chunk

    # Get filename for download header
    filename = file_path.split("/")[-1]

    return Response(
        stream_with_context(generate_file_stream()),
        content_type="application/octet-stream",
        headers={
            "Content-Disposition": f"attachment; filename='{filename}'"
        }
    )


@storage_bp.route("/copy", methods=["POST"])
@validate_request(FileCopyInput)
@flask_pydantic_response
def copy_file_from_source_to_destination(payload: FileCopyInput):
    source_bucket = get_company_bucket_name(payload.sourceCompany)
    destination_bucket = get_company_bucket_name(payload.destinationCompany)
    destination_blob = copy_file(source_bucket, payload.sourcePath, destination_bucket, payload.destinationPath)
    return Status200(message=f"File copied to {destination_bucket}/{destination_blob.name} successfully"), 200
