from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response
from flask_utils.models_config.model_utils import map_to_model

from src.api.models import GetGithubInstallationOutput, SecretsOutput, RepositoryMetadata
from src.api.routes.secret_manager import get_github_secret_value, get_github_secret_value_with_access_token
from src.error.errors import (AzureBaseError, ResourceNotFound, SecretNotFound,
                              TokenExpiredError)
from src.github.github_app_connection import GithubAppConnection
from src.service.azure_service import fetch_azure_secret
from src.service.git_installation_service import get_active_installation_by_target_id
from src.service.github_installation_access_service import (
    fetch_github_secret, get_active_github_installation_by_repo_id)
from src.service.github_integration_service import \
    get_github_installation_by_target_id
from src.service.github_project_repo_service import \
    get_github_project_repo_by_repo_id

repositories_bp = Blueprint("repositories", __name__, url_prefix="/repositories")


@repositories_bp.route("/<repo_id>/installation", methods=["GET"])
@flask_pydantic_response
def get_installation_info_by_repository_id(repo_id: str):
    """Get the github repository by project id. This API explicitly checks
    for the existence of the repository in the database and then only returns the installation info
    """
    project_github_repo = get_github_project_repo_by_repo_id(repo_id)
    if not project_github_repo:
        raise ResourceNotFound(f"Repository with id {repo_id} not found in the database.")

    github_installation = get_github_installation_by_target_id(project_github_repo.org_name)
    if not github_installation:
        raise ResourceNotFound(f"Active installation for repository with id {repo_id} not found.")

    response = map_to_model(github_installation, GetGithubInstallationOutput)
    return response, 200


# ------------------------------------------------------------------------------
# Secret Fetching Functions and Endpoint by SCM Type
# ------------------------------------------------------------------------------


# Mapping of supported Source Control Management (SCM) systems to their respective
# secret-fetching functions. This is used to dynamically delegate the logic for
# retrieving access tokens or secrets based on the repository's configured SCM type.
#
# Keys:
#   - VersionControlSystem.GITHUB: GitHub-hosted repositories
#   - VersionControlSystem.AZURE_DEVOPS: Azure DevOps-hosted repositories
#
# Values:
#   - fetch_github_secret: function to fetch GitHub secrets
#   - fetch_azure_secret: function to fetch Azure DevOps secrets
fetch_secret_by_scm = {
    VersionControlSystem.GITHUB: fetch_github_secret,
    VersionControlSystem.AZURE_DEVOPS: fetch_azure_secret,
}


@repositories_bp.route("/<repo_id>/access-token", methods=["GET"])
@flask_pydantic_response
def get_repository_access_token(repo_id: str):
    """
    Retrieve an SCM access token for the given repository ID.

    This endpoint determines the Source Code Management (SCM) provider (e.g., GitHub or Azure DevOps)
    associated with the given repository ID, then delegates the token-fetching logic to the appropriate
    implementation based on the SCM type.

    Supported SCMs:
    - GitHub
    - Azure DevOps

    :param repo_id: The unique identifier of the repository.
    :return: A SecretsOutput object containing the access token and SCM type, returned with HTTP 200.
    :raises ResourceNotFound:
        - If the repository is not found.
        - If no active installation or tenant context is found.
        - If the access token could not be retrieved from the SCM provider.
    :raises ValueError: If the SCM type is not supported or improperly configured.
    :raises TokenExpiredError: If the SCM token is expired and cannot be refreshed.
    :raises AzureBaseError: If there's an error retrieving the access token from Azure DevOps.
    """
    logger.info(f"[AccessToken] Fetching SCM access token for repo_id: {repo_id}")

    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        logger.error(f"[AccessToken] No active installation found for repo_id: {repo_id}")
        raise ResourceNotFound(f"Project Repository SCM Field with repo_id {repo_id} not found in the database.")

    logger.info(f"[AccessToken] Found active installation for repo_id: {repo_id}, svc_type: {installation.svc_type}")

    fetch_token_fn = fetch_secret_by_scm.get(installation.svc_type)
    if not fetch_token_fn:
        logger.error(f"[AccessToken] Unsupported SCM type '{installation.svc_type}' for repo_id: {repo_id}")
        raise ValueError(f"Unsupported SCM type: {installation.svc_type}.")

    try:
        token_data = fetch_token_fn(repo_id, installation.installation_id)
    except TokenExpiredError:
        raise
    except Exception as e:
        logger.error(
            f"[AccessToken] Error retrieving access token for repo_id: {repo_id} "
            f"from {installation.svc_type}: {str(e)}"
        )
        raise AzureBaseError(
            f"[AccessToken] Error retrieving access token for repo_id: {repo_id} from {installation.svc_type}"
        )

    if not token_data:
        logger.error(
            f"[AccessToken] Access token for repo_id '{repo_id}' could not be retrieved from {installation.svc_type}"
        )
        raise ResourceNotFound(
            f"Access token for repo_id '{repo_id}' could not be retrieved from {installation.svc_type}."
        )

    logger.info(f"[AccessToken] Successfully retrieved access token for repo_id: {repo_id}")
    return token_data, 200


@repositories_bp.route("/<repo_id>/secret", methods=["GET"])
@flask_pydantic_response
def get_github_secrets_by_repo_id(repo_id: str):
    """
    Retrieves the GitHub secrets for the repository specified by the given repository ID.
    This function interacts with the associated GitHub installation and fetches the
    secrets based on the repository's details stored in the underlying database. If no
    repository or installation is found for the provided ID, appropriate exceptions
    are raised.
    """
    try:
        project_github_repo = get_github_project_repo_by_repo_id(repo_id)
        if not project_github_repo:
            raise ResourceNotFound(f"Repository with id {repo_id} not found in the database.")

        github_installation = get_github_installation_by_target_id(project_github_repo.org_name)
        if not github_installation:
            raise ResourceNotFound(f"Active installation for repository with id {repo_id} not found.")

        secret = get_github_secret_value(github_installation.installation_id, "latest")
        response = SecretsOutput(
            accessToken=secret.get("access_token"),
            code=secret.get("code"),
            installationID=secret.get("installation_id"),
            setupAction=secret.get("setup_action"),
            scvType=github_installation.svc_type
        )
        return response, 200
    except SecretNotFound:
        logger.error(f"GitHub secrets not found for repository with ID {repo_id}")
        raise
    except ResourceNotFound:
        logger.error(f"Repository with ID {repo_id} not found in the database.")
        raise
    except Exception as e:
        logger.error(f"Failed to retrieve GitHub secrets for repository with ID {repo_id}: {e}")
        return {"message": "Failed to retrieve GitHub secrets for repository with ID %s" % repo_id}, 500


@repositories_bp.route("/<repo_id>/secret/access-token", methods=["GET"])
@flask_pydantic_response
def get_github_app_access_toke_by_repo_id(repo_id: str):
    try:
        project_github_repo = get_github_project_repo_by_repo_id(repo_id)
        if not project_github_repo:
            raise ResourceNotFound(f"Repository with id {repo_id} not found in the database.")

        github_installation = get_github_installation_by_target_id(project_github_repo.org_name)
        if not github_installation:
            raise ResourceNotFound(f"Active installation for repository with id {repo_id} not found.")

        installation_id = github_installation.installation_id
        secret = get_github_secret_value_with_access_token(installation_id, "latest")

        response = SecretsOutput(
            accessToken=secret.get("access_token"),
            code=secret.get("code"),
            installationID=installation_id,
            setupAction=secret.get("setup_action"),
            scvType="GITHUB"
        )
        return response, 200
    except SecretNotFound:
        logger.error(f"GitHub secrets not found for repository with ID {repo_id}")
        raise
    except ResourceNotFound:
        logger.error(f"Repository with ID {repo_id} not found in the database.")
        raise
    except Exception as e:
        logger.error(f"Failed to retrieve GitHub secrets for repository with ID {repo_id}: {e}")
        return {"message": "Failed to retrieve GitHub secrets for repository with ID %s" % repo_id}, 500


@repositories_bp.route("/<repo_id>", methods=["GET"])
@flask_pydantic_response
def get_repository_by_id(repo_id: str):
    repo = get_github_project_repo_by_repo_id(repo_id)
    if not repo:
        raise ResourceNotFound(f"Repository with id {repo} not found in the database.")

    org_info = get_active_installation_by_target_id(repo.org_id)
    if not org_info:
        raise ResourceNotFound(f"Active installation for repository with id {repo} not found.")

    metadata = map_to_model(repo, RepositoryMetadata)
    metadata.installationType = org_info.installation_type

    return metadata, 200
