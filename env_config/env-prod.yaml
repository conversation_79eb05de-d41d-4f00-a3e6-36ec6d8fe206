SERVICE_NAME: archie-service-admin
PORT: 8080
IMAGE_TAG: latest
DEPLOYMENT_REGION: us-central1
PROJECT_ID: blitzy-platform-prod
REPOSITORY: gcf-artifacts
IMAGE_NAME: archie-service-admin
SPANNER_DATABASE_NAME: projects/blitzy-platform-prod/instances/blitzy-internal/databases/blitzy-os-db
LOG_LEVEL: INFO
SERVICE_ACCOUNT: <EMAIL>
CONNECTOR_NAME: platform-connector-prod
EGRESS_SETTING: all-traffic
ARTIFACTORY_REGION: us-east1
GCS_BUCKET_NAME: blitzy-platform-prod
JOB_CPU_REQUEST: 1
JOB_CPU_LIMIT: 2
JOB_MEMORY_REQUEST: 2Gi
JOB_MEMORY_LIMIT: 4Gi
GKE_ZONE: us-central1-a
GKE_CLUSTER: blitzy-internal
ENVIRONMENT: prod
K_SERVICE: true
WINDOWS_JOB_IMAGE: winamd64/python:3.12-windowsservercore-ltsc2022
WINDOWS_JOB_REQUESTS_CPU: 1
WINDOWS_JOB_REQUESTS_MEMORY: 1 # In GB
WINDOWS_JOB_LIMITS_CPU: 2
WINDOWS_JOB_LIMITS_MEMORY: 32 # In GB
WINDOWS_JOB_NAMESPACE: default
GCP_SERVICE_ACCOUNT_SECRET_NAME: google-service-account
