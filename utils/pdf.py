from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient


def convert_to_pdf(project_name: str, repo_url: str):
    """
    Send a POST request to convert markdown to PDF.

    Args:
        markdown_content (str): The markdown content to convert
        project_name (str): Name of the project
        repo_url (str): Repository URL

    Returns:
        Response: The response from the PDF conversion service
    """
    payload = {
        "projectName": project_name,
        "repoUrl": repo_url,
        "docType": "tech_spec"
    }

    try:
        with ServiceClient() as client:
            response = client.post("markdown", "/v1/pdf/convert", json=payload, timeout=120)

            return response
    except Exception as e:
        logger.error(f"Failed to convert markdown to PDF: {str(e)}")
        raise Exception(f"PDF conversion failed: {str(e)}")
