from blitzy_utils.logger import logger
from flask import Flask
from flask_cors import CORS
from flask_utils.middleware.request_context import RequestContextMiddleware

from src.api.routes.job import job_bp
from src.api.routes.project_info import project_info_bp
from src.api.routes.storage import storage_bp
from src.api.routes.subscriptions import subscription_bp
from src.api.routes.utils import utils_bp
from src.consts import (AUTHORIZATION_HEADER_NAME, BASIC_AUTH_HEADER_NAME,
                        ID_TOKEN_HEADER_NAME)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
CORS(app, resources={r"/*": {"origins": "*", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
                             "allow_headers": ["Content-Type", AUTHORIZATION_HEADER_NAME, ID_TOKEN_HEADER_NAME,
                                               BASIC_AUTH_HEADER_NAME]}})

# Register blueprints
app.register_blueprint(utils_bp)
app.register_blueprint(project_info_bp)
app.register_blueprint(storage_bp)
app.register_blueprint(job_bp)
app.register_blueprint(subscription_bp)

# Add your additional blueprints here
# Example:
# from src.api.routes.your_route import your_bp
# app.register_blueprint(your_bp)

middleware = RequestContextMiddleware(app, logger=logger)


def initialize_app() -> None:
    """Initialize application dependencies."""
    # Add your initialization code here
    # Examples:
    # - Database connections
    # - Cache clients
    # - External service clients
    logger.info("Initializing application dependencies...")
    pass


if __name__ == "__main__":
    logger.info("Starting the server...")
    initialize_app()
    app.run(host="localhost", port=8081, debug=True)
