openapi: 3.0.0
info:
  title: Flask Microservice API
  version: 1.0.0
  description: Template API for Flask Microservices
  contact:
    email: chaitany<PERSON>@blitzy.com

paths:
  /v1/job/create-job:
    post:
      summary: Create a GKE job
      description: Creates a Kubernetes job in Google Kubernetes Engine with specified configuration
      tags:
      - Job Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateJobInput'
      responses:
        '200':
          description: Job created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateJobOutput'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status500'

  /v1/job/windows/trigger:
    post:
      summary: Trigger Windows job from Pub/Sub
      description: Handles Pub/Sub push messages to trigger Windows jobs in Kubernetes
      tags:
      - Job Management
      responses:
        '200':
          description: Event processed successfully
          content:
            text/plain:
              schema:
                type: string
                example: Event processed successfully
        '400':
          description: Invalid Pub/Sub message format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status500'

  /project/info/{projectId}:
    get:
      summary: Get project information
      description: Retrieves information about a specific project
      parameters:
      - in: path
        name: projectId
        required: true
        schema:
          type: string
        description: Unique identifier of the project
      responses:
        '200':
          description: Successfully retrieved project information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectInfoOutput'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /v1/storage/bucket-walk:
    get:
      summary: Walk through GCS bucket structure
      description: Returns the directory structure of a GCS bucket, similar to os.walk()
      tags:
      - Storage
      parameters:
      - in: query
        name: company_id
        required: true
        schema:
          type: string
        description: Unique identifier of the company
        example: company-123
      - in: query
        name: prefix
        required: false
        schema:
          type: string
          default: ''
        description: Optional prefix to start walking from a specific directory
        example: documents/projects/
      responses:
        '200':
          description: Successfully retrieved bucket structure
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BucketWalkOutput'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status500'

  /storage/upload:
    post:
      summary: Upload a file
      description: Uploads a file to storage with specified company and file path
      parameters:
      - in: query
        name: company_id
        required: true
        schema:
          type: string
        description: Unique identifier of the company
        example: company-123
      - in: query
        name: file_path
        required: true
        schema:
          type: string
        description: Destination path for the uploaded file
        example: documents/project-files/report.pdf
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FileUploadInput'
      responses:
        '201':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadOutput'
        '400':
          description: Invalid request parameters or file
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /storage/upload-string:
    post:
      summary: Upload file content as string
      description: Uploads file content provided as a string to storage with specified company and file path
      parameters:
      - in: query
        name: company_id
        required: true
        schema:
          type: string
        description: Unique identifier of the company
        example: company-123
      - in: query
        name: file_path
        required: true
        schema:
          type: string
        description: Destination path for the uploaded file
        example: documents/project-files/config.json
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StringUploadInput'
      responses:
        '201':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadOutput'
        '400':
          description: Invalid request parameters or content
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /storage/download:
    get:
      summary: Download a file
      description: Downloads a file from storage, optionally returning content as text
      parameters:
      - in: query
        name: company_id
        required: true
        schema:
          type: string
        description: Unique identifier of the company
        example: company-123
      - in: query
        name: file_path
        required: true
        schema:
          type: string
        description: Path of the file to download
        example: documents/project-files/report.pdf
      - in: query
        name: as_text
        required: false
        schema:
          type: boolean
          default: false
        description: Whether to return file content as text instead of binary
        example: true
      responses:
        '200':
          description: File downloaded successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: Binary file content (when as_text=false)
            text/plain:
              schema:
                type: string
                description: Text file content (when as_text=true)
                example: This is the content of the file as text...
          headers:
            Content-Disposition:
              description: Attachment filename
              schema:
                type: string
                example: attachment; filename="report.pdf"
            Content-Type:
              description: MIME type of the file
              schema:
                type: string
                example: application/pdf
            Content-Length:
              description: Size of the file in bytes
              schema:
                type: integer
                example: 2048576
        '404':
          description: File not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /health-check:
    get:
      tags:
      - System
      summary: Health check endpoint
      description: Checks if the service is running
      operationId: healthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status200'
        '500':
          description: Service unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status500'

  /uptime-check:
    get:
      tags:
      - System
      summary: Uptime check endpoint
      description: Returns current timestamp for uptime monitoring
      responses:
        '200':
          description: Current timestamp
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessed_ts:
                    type: string
                    format: date-time
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status500'

  /subscription/user/{user_id}/plan:
    get:
      summary: Get user's subscription plan
      description: Retrieves the subscription plan for a specific user
      parameters:
      - name: user_id
        in: path
        required: true
        description: Unique identifier of the user
        schema:
          type: string
          example: '12345'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlan'
        '404':
          description: User not found
        '500':
          description: Internal server error

  /storage/copy:
    post:
      summary: Copy a file
      description: Copies a file from one location to another, potentially across companies and buckets
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileCopyInput'
      responses:
        '201':
          description: File copied successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status200'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '404':
          description: Source file not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'
        '409':
          description: File already exists at destination
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status409'

components:
  schemas:
    Status200:
      type: object
      properties:
        message:
          type: string
          example: OK

    Status201:
      type: object
      properties:
        id:
          type: string
          example: 123e4567-e89b-12d3-a456-426614174000
        message:
          type: string
          example: Created Successfully

    Status400:
      type: object
      properties:
        message:
          type: string
          example: Bad Request

    Status401:
      type: object
      properties:
        message:
          type: string
          example: Unauthorized

    Status403:
      type: object
      properties:
        message:
          type: string
          example: Forbidden

    Status404:
      type: object
      properties:
        message:
          type: string
          example: Not Found

    Status409:
      type: object
      properties:
        message:
          type: string
          example: Conflict

    Status500:
      type: object
      properties:
        message:
          type: string
          example: Internal Server Error

    ErrorResponse:
      type: object
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Error message
        details:
          type: object
          description: Additional error details

    BucketWalkItem:
      type: object
      properties:
        path:
          type: string
          description: Directory path in the bucket
          example: documents/projects/
        subfolders:
          type: array
          description: List of subfolders within this directory
          items:
            type: string
          example: [documents/projects/project1/, documents/projects/project2/]
        files:
          type: array
          description: List of files within this directory
          items:
            type: string
          example: [documents/projects/readme.txt, documents/projects/config.json]

    BucketWalkOutput:
      type: object
      properties:
        bucket_name:
          type: string
          description: Name of the GCS bucket that was walked
          example: blitzy-os-internal-company-123
        prefix:
          type: string
          description: Prefix used for walking the bucket
          example: documents/projects/
        results:
          type: array
          description: List of directory structures found in the bucket
          items:
            $ref: '#/components/schemas/BucketWalkItem'

    ProjectInfoOutput:
      type: object
      properties:
        companyId:
          type: string
          description: Unique identifier of the company associated with the project
          example: company-789
        repoIds:
          type: array
          description: List of repository IDs associated with the project
          items:
            type: string
          example: [repo-123, repo-456, repo-789]
        branchIds:
          type: array
          description: List of branch IDs associated with the project
          items:
            type: string
          example: [branch-abc, branch-def, branch-ghi]

    FileUploadInput:
      type: object
      properties:
        file:
          type: string
          format: binary
          description: The file to be uploaded
      required:
      - file

    StringUploadInput:
      type: object
      properties:
        data:
          type: string
          description: The file content as a string
          example: This is string
          minimum: 1
        contentType:
          type: string
          description: MIME type of the content (optional)
          example: application/json
        companyId:
          type: string
          description: ID of the company
          minimum: 1
        filePath:
          type: string
          description: Path of the file
          minimum: 1
      required:
      - data
      - companyId
      - filePath

    FileUploadOutput:
      type: object
      properties:
        filePath:
          type: string
          description: Path of the uploaded file

    CreateJobInput:
      type: object
      properties:
        job_name:
          type: string
          description: Name of the job
          example: reverse-code-generator
        image:
          type: string
          description: Container image for the job
          example: gcr.io/project-id/windows-job:latest
        execution_type:
          type: string
          description: Type of execution environment for the job
          enum:
          - LINUX
          - WINDOWS
          default: WINDOWS
        windows_job_requests_cpu:
          type: string
          description: CPU requests for the job
          example: '1'
        windows_job_requests_memory:
          type: string
          description: Memory requests for the job in Gi
          example: '1'
        windows_job_limits_cpu:
          type: string
          description: CPU limits for the job
          example: '2'
        windows_job_limits_memory:
          type: string
          description: Memory limits for the job in Gi
          example: '2'
        command:
          type: array
          description: Command to execute in the container
          items:
            type: string
          example: [powershell, -Command]
        args:
          type: array
          description: Arguments to pass to the command
          items:
            type: string
          example: [Write-Host 'Hello World']
        env_vars:
          type: object
          description: Environment variables for the job
          additionalProperties: true
          example:
            ENV_VAR_1: value1
            ENV_VAR_2: value2

    CreateJobOutput:
      type: object
      properties:
        status:
          type: string
          description: Status of the job creation
          example: success
        message:
          type: string
          description: Success message with job details
          example: Job local-windows-job-abc12345 created successfully

    PubSubMessageInput:
      type: object
      properties:
        message:
          type: object
          description: Pub/Sub message envelope
          properties:
            data:
              type: string
              description: Base64-encoded message data
              example: eyJpbWFnZSI6ICJnY3IuaW8vcHJvamVjdC1pZC93aW5kb3dzLWpvYjpsYXRlc3QiLCAiZW52X3ZhcnMiOiB7IkVOVl9WQVJfMSI6ICJ2YWx1ZTEifX0=
            attributes:
              type: object
              description: Message attributes
              additionalProperties:
                type: string
      required:
      - message

    UserPlan:
      type: object
      properties:
        planName:
          type: string
          description: Name of the subscription plan
          example: Premium
      required:
      - planName

    FileCopyInput:
      type: object
      properties:
        sourceCompany:
          type: string
          description: ID of the company that owns the source file
          example: "company-123"
        sourcePath:
          type: string
          description: Path of the source file to copy
          example: "documents/project-files/report.pdf"
        destinationCompany:
          type: string
          description: ID of the company for the destination
          example: "company-456"
        destinationPath:
          type: string
          description: Destination bucket or path for the copied file
          example: "backups/archived-files/report.pdf"
      required:
        - sourcePath
        - destinationPath
